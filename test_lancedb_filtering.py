#!/usr/bin/env python3
"""
Test script for LanceDB filtering functionality.
This script tests the LanceEmbeddingManager to ensure it properly handles 'IN' operator filtering.
"""

import os
import sys
import tempfile
import shutil
from typing import List, Dict

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.embeddings_management import <PERSON><PERSON>mbeddingManager

def test_in_operator_filtering():
    """Test the 'IN' operator filtering that was problematic with ChromaDB."""
    print("=" * 60)
    print("Testing LanceDB 'IN' Operator Filtering")
    print("=" * 60)
    
    temp_dir = tempfile.mkdtemp(prefix="lancedb_filter_test_")
    print(f"Using temporary directory: {temp_dir}")
    
    try:
        # Initialize LanceDB manager
        lance_manager = LanceEmbeddingManager(
            embedding_model="all-MiniLM-L6-v2",
            persist_directory=temp_dir
        )
        
        # Test data with different categories
        test_data = [
            {"label_id": 1, "name": "Machine Learning", "category": "Concept"},
            {"label_id": 2, "name": "<PERSON> Einstein", "category": "Persons"},
            {"label_id": 3, "name": "Harvard University", "category": "Institutions"},
            {"label_id": 4, "name": "Artificial Intelligence", "category": "Concept"},
            {"label_id": 5, "name": "Marie Curie", "category": "Persons"},
            {"label_id": 6, "name": "World War II", "category": "Times and Places"},
        ]
        
        collection_name = "filter_test_collection"
        
        print(f"\n1. Creating collection and adding {len(test_data)} test records...")
        
        # Create collection and add data
        result = lance_manager.create_collection(collection_name)
        lance_manager.upsert_data(collection_name, test_data)
        print("✓ Data added successfully")
        
        # Test 1: Simple equality filter
        print("\n2. Testing simple equality filter...")
        query_results = lance_manager.query(collection_name, 'category == "Concept"', ["name", "category"])
        print(f"Results for category == 'Concept': {[r['name'] for r in query_results]}")
        assert len(query_results) == 2, f"Expected 2 results, got {len(query_results)}"
        print("✓ Simple equality filter works")
        
        # Test 2: IN operator with list format
        print("\n3. Testing 'IN' operator with list format...")
        category_list = ["Concept", "Persons"]
        filter_str = f'category in {category_list}'
        query_results = lance_manager.query(collection_name, filter_str, ["name", "category"])
        result_names = [r['name'] for r in query_results]
        print(f"Results for category in {category_list}: {result_names}")
        assert len(query_results) == 4, f"Expected 4 results, got {len(query_results)}"
        print("✓ 'IN' operator with list format works")
        
        # Test 3: IN operator in search (the problematic case from attach_label.py)
        print("\n4. Testing 'IN' operator in search...")
        category_map = {"keywords": ["Concept"], "persons_organizations": ["Persons", "Institutions"]}
        keywords_type = "persons_organizations"
        filter_str = f'category in {category_map[keywords_type]}'
        
        search_results = lance_manager.search(
            collection_name=collection_name,
            query_texts=["scientist"],
            top_k=3,
            simple_output=True,
            filter=filter_str
        )
        print(f"Search results with filter '{filter_str}': {search_results}")
        # Should return names from Persons and Institutions categories
        assert len(search_results) > 0, "Should return some results"
        print("✓ 'IN' operator in search works")
        
        # Test 4: Test the exact filter format from attach_label.py
        print("\n5. Testing exact filter format from attach_label.py...")
        category_map = {"keywords": ["Concept"], "persons_organizations": ["Persons", "Institutions"], "times_places": ["Times and Places"]}
        for keywords_type in category_map:
            filter_str = f'category in {category_map[keywords_type]}'
            search_results = lance_manager.search(
                collection_name=collection_name,
                query_texts=["test query"],
                top_k=3,
                simple_output=True,
                filter=filter_str
            )
            print(f"Filter '{filter_str}' returned {len(search_results)} results: {search_results}")
        
        print("✓ All filter formats from attach_label.py work correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"Warning: Could not clean up {temp_dir}: {e}")

def main():
    """Run the filtering test."""
    print("LanceDB Filtering Test Suite")
    print("=" * 80)
    
    success = test_in_operator_filtering()
    
    # Final result
    print("\n" + "=" * 80)
    if success:
        print("🎉 ALL FILTERING TESTS PASSED!")
        print("✅ LanceDB 'IN' operator filtering is working correctly.")
        print("✅ The filtering limitations from ChromaDB have been resolved.")
    else:
        print("❌ FILTERING TESTS FAILED!")
    print("=" * 80)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
