from typing import List, Dict, Optional
from math import ceil
import os
import platform
import numpy as np
from sentence_transformers import SentenceTransformer
import pandas as pd
import pyarrow as pa

from .csv_converter import csv_to_json

class LanceEmbeddingManager:
    """LanceDB-based embedding manager for all platforms."""

    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2", openai_api_key: Optional[str] = None, persist_directory: str = "./lance_db"):
        """Initialize the LanceDB embedding manager."""
        self.embedding_model = embedding_model
        self.openai_api_key = openai_api_key
        self.persist_directory = persist_directory

        # Initialize LanceDB client
        try:
            import lancedb

            # Create database connection
            self.lance_db = lancedb.connect(persist_directory)
            print(f"Initialized LanceDB client with persist directory: {persist_directory}")
        except Exception as e:
            print(f"Error initializing LanceDB client: {e}")
            raise

        # Initialize SentenceTransformer for embeddings
        try:
            self.sentence_transformer = SentenceTransformer(embedding_model)
            print(f"Initialized SentenceTransformer with model: {embedding_model}")
        except Exception as e:
            print(f"Error initializing SentenceTransformer: {e}")
            self.sentence_transformer = None

    def create_collection(self, collection_name: str = "default"):
        """Create a LanceDB table (collection)."""
        try:
            # Check if table already exists
            existing_tables = self.lance_db.table_names()
            if collection_name in existing_tables:
                print(f"Table '{collection_name}' already exists.")
                return "exists"

            # Create empty table with schema - we'll add data later
            # LanceDB requires at least one record to create a table, so we'll create it when data is first added
            print(f"LanceDB table '{collection_name}' will be created when data is first added.")
            return "success"
        except Exception as e:
            print(f"Error creating table '{collection_name}': {e}")
            return "error"

    def upsert_data(self, collection_name: str, data: List[Dict]):
        """Insert data into the LanceDB table dynamically in batches."""
        try:
            if not self.sentence_transformer:
                print("Warning: No sentence transformer available, skipping embedding generation")
                return

            batch_size = 10000  # LanceDB can handle larger batches efficiently

            # Split data into batches
            num_batches = ceil(len(data) / batch_size)

            for batch_index in range(num_batches):
                start_index = batch_index * batch_size
                end_index = min((batch_index + 1) * batch_size, len(data))
                batch_data = data[start_index:end_index]

                # Prepare data for LanceDB
                processed_data = []
                texts_to_embed = []

                for item in batch_data:
                    # Extract text for embedding
                    text = item.get("name", "")
                    texts_to_embed.append(text)

                # Generate embeddings for the batch
                embeddings = self.sentence_transformer.encode(texts_to_embed).tolist()

                # Create records with embeddings
                for i, item in enumerate(batch_data):
                    record = {
                        "label_id": item.get("label_id", i),
                        "name": item.get("name", ""),
                        "vector": embeddings[i],  # LanceDB uses 'vector' as the standard embedding column name
                    }

                    # Add all other fields as metadata
                    for key, value in item.items():
                        if key not in ["name", "label_id", "embedding", "vector"]:
                            record[key] = value

                    processed_data.append(record)

                # Convert to DataFrame for LanceDB
                df = pd.DataFrame(processed_data)

                # Create or append to table
                try:
                    # Try to get existing table
                    table = self.lance_db.open_table(collection_name)
                    # Add data to existing table
                    table.add(df)
                except Exception:
                    # Table doesn't exist, create it
                    table = self.lance_db.create_table(collection_name, df)

                print(f"LanceDB upserted {len(batch_data)} records into table '{collection_name}' (Batch {batch_index + 1}/{num_batches}).")

        except Exception as e:
            print(f"Error upserting data to table '{collection_name}': {e}")

    def load_collection(self, collection_name: str, load_fields=None, skip_load_dynamic_field=True):
        """Load collection - no-op for LanceDB as tables are always ready."""
        return "Using LanceDB, no need to load collection."

    def search(self, collection_name: str, query_texts: List[str], top_k: int = 3,
               simple_output: bool = True, filter: Optional[str] = None,
               output_fields: Optional[List[str]] = None, search_params: Optional[Dict] = None):
        """Search for similar texts in the LanceDB table."""
        try:
            table = self.lance_db.open_table(collection_name)

            if not self.sentence_transformer:
                print("Warning: No sentence transformer available for search")
                return []

            # Generate embeddings for query texts
            query_embeddings = self.sentence_transformer.encode(query_texts).tolist()

            # Convert filter to SQL where clause if provided
            where_clause = None
            if filter:
                where_clause = self._convert_filter_to_sql(filter)

            # Perform search for each query embedding
            all_results = []
            for query_embedding in query_embeddings:
                # Build search query
                search_query = table.search(query_embedding).limit(top_k)

                # Apply filter if provided
                if where_clause:
                    search_query = search_query.where(where_clause)

                # Execute search
                results = search_query.to_pandas()
                all_results.append(results)

            if simple_output:
                # Return unique document names
                unique_names = set()
                for result_df in all_results:
                    if not result_df.empty and 'name' in result_df.columns:
                        unique_names.update(result_df['name'].tolist())
                return list(unique_names)
            else:
                # Return results in a format similar to Milvus
                formatted_results = []
                for result_df in all_results:
                    batch_results = []
                    for _, row in result_df.iterrows():
                        result_item = {
                            'entity': {
                                'name': row.get('name', '')
                            }
                        }

                        # Add all other fields as metadata
                        for col in result_df.columns:
                            if col not in ['name', 'vector', '_distance']:
                                result_item['entity'][col] = row[col]

                        # Add distance/score if available
                        if '_distance' in result_df.columns:
                            result_item['distance'] = row['_distance']

                        batch_results.append(result_item)

                    formatted_results.append(batch_results)

                return formatted_results

        except Exception as e:
            print(f"Error searching table '{collection_name}': {e}")
            return []

    def query(self, collection_name, filter, output_fields):
        """Query operation for LanceDB."""
        try:
            table = self.lance_db.open_table(collection_name)

            # Convert filter to SQL where clause
            where_clause = None
            if filter:
                where_clause = self._convert_filter_to_sql(filter)

            # Build query
            if where_clause:
                # Query with filter
                results_df = table.search().where(where_clause).to_pandas()
            else:
                # Query all data
                results_df = table.to_pandas()

            # Format results to match expected structure
            formatted_results = []
            for _, row in results_df.iterrows():
                result_item = {'name': row.get('name', '')}

                # Add all other fields
                for col in results_df.columns:
                    if col not in ['name', 'vector']:
                        result_item[col] = row[col]

                formatted_results.append(result_item)

            return formatted_results

        except Exception as e:
            print(f"Error querying table '{collection_name}': {e}")
            return []

    def delete_collection(self, collection_name: str):
        """Delete a LanceDB table."""
        try:
            self.lance_db.drop_table(collection_name)
            print(f"LanceDB table '{collection_name}' deleted successfully.")
        except Exception as e:
            print(f"Error deleting table '{collection_name}': {e}")

    def _convert_filter_to_sql(self, filter_str: str) -> str:
        """Convert Milvus-style filter to LanceDB SQL where clause."""
        if not filter_str:
            return None

        # Handle 'IN' operator - this is the key improvement over ChromaDB
        if ' in ' in filter_str.lower():
            # Parse filter like: 'category in ["Concept", "Person"]' or 'category in {category_list}'
            parts = filter_str.split(' in ')
            if len(parts) == 2:
                field = parts[0].strip()
                values_part = parts[1].strip()

                # Handle different formats of the values part
                if values_part.startswith('[') and values_part.endswith(']'):
                    # List format: ["value1", "value2"]
                    values_str = values_part[1:-1]  # Remove brackets
                    values = [v.strip().strip('"\'') for v in values_str.split(',')]
                elif values_part.startswith('{') and values_part.endswith('}'):
                    # Set format: {"value1", "value2"} - convert to list format for SQL
                    values_str = values_part[1:-1]  # Remove braces
                    values = [v.strip().strip('"\'') for v in values_str.split(',')]
                else:
                    # Assume it's a variable that evaluates to a list - try to parse as string
                    # This handles cases like: category in ['Concept', 'Person']
                    import ast
                    try:
                        values = ast.literal_eval(values_part)
                        if not isinstance(values, (list, tuple, set)):
                            values = [str(values)]
                    except:
                        # Fallback: treat as single value
                        values = [values_part.strip().strip('"\'')]

                # Create SQL IN clause
                quoted_values = [f"'{v}'" for v in values]
                return f"{field} IN ({', '.join(quoted_values)})"

        # Handle equality operator
        elif '==' in filter_str:
            parts = filter_str.split('==')
            if len(parts) == 2:
                field = parts[0].strip()
                value = parts[1].strip().strip('"\'')
                return f"{field} = '{value}'"

        # Handle other operators (>, <, >=, <=, !=)
        for op in ['>=', '<=', '!=', '>', '<']:
            if op in filter_str:
                parts = filter_str.split(op)
                if len(parts) == 2:
                    field = parts[0].strip()
                    value = parts[1].strip().strip('"\'')
                    # Convert != to <>
                    sql_op = '<>' if op == '!=' else op
                    return f"{field} {sql_op} '{value}'"

        # If no recognized pattern, return the filter as-is (LanceDB might handle it)
        return filter_str

class ChromaEmbeddingManager:
    """ChromaDB-based embedding manager for Windows environments."""

    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2", openai_api_key: Optional[str] = None, persist_directory: str = "./chroma_db"):
        """Initialize the ChromaDB embedding manager."""
        self.embedding_model = embedding_model
        self.openai_api_key = openai_api_key
        self.persist_directory = persist_directory

        # Initialize ChromaDB client
        try:
            import chromadb
            from chromadb.config import Settings

            # Create persistent client
            self.chroma_client = chromadb.PersistentClient(
                path=persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            print(f"Initialized ChromaDB client with persist directory: {persist_directory}")
        except Exception as e:
            print(f"Error initializing ChromaDB client: {e}")
            raise

        # Initialize sentence transformer for embeddings
        try:
            self.sentence_transformer = SentenceTransformer(embedding_model)
            print(f"Initialized SentenceTransformer with model: {embedding_model}")
        except Exception as e:
            print(f"Warning: Could not initialize SentenceTransformer: {e}")
            self.sentence_transformer = None

    def create_collection(self, collection_name: str = "default"):
        """Create a ChromaDB collection."""
        try:
            # Check if collection already exists
            existing_collections = [col.name for col in self.chroma_client.list_collections()]
            if collection_name in existing_collections:
                print(f"Collection '{collection_name}' already exists.")
                return "exists"

            # Create new collection with cosine similarity
            self.collection = self.chroma_client.create_collection(
                name=collection_name,
                metadata={"hnsw:space": "cosine"}
            )
            print(f"ChromaDB collection '{collection_name}' created successfully.")
            return "success"
        except Exception as e:
            print(f"Error creating collection '{collection_name}': {e}")
            return "error"

    def upsert_data(self, collection_name: str, data: List[Dict]):
        """Insert data into the ChromaDB collection dynamically in batches."""
        try:
            # Get or create collection
            try:
                collection = self.chroma_client.get_collection(collection_name)
            except Exception:
                # Collection doesn't exist, create it
                self.create_collection(collection_name)
                collection = self.chroma_client.get_collection(collection_name)

            if not self.sentence_transformer:
                print("Warning: No sentence transformer available, skipping embedding generation")
                return

            batch_size = 5000  # ChromaDB has a lower maximum batch size than Milvus

            # Split data into batches
            num_batches = ceil(len(data) / batch_size)

            for batch_index in range(num_batches):
                # Get the current batch of data
                start_idx = batch_index * batch_size
                end_idx = min((batch_index + 1) * batch_size, len(data))
                batch_data = data[start_idx:end_idx]

                # Extract texts and generate embeddings for the current batch
                texts = [item["name"] for item in batch_data]
                embeddings = self.sentence_transformer.encode(texts).tolist()

                # Prepare data for ChromaDB
                ids = [str(item.get("label_id", start_idx + i)) for i, item in enumerate(batch_data)]
                documents = texts
                metadatas = []

                for item in batch_data:
                    metadata = {}
                    for key, value in item.items():
                        if key not in ["name", "label_id", "embedding"]:
                            # ChromaDB metadata values must be strings, numbers, or booleans
                            if value is not None:
                                metadata[key] = str(value) if not isinstance(value, (int, float, bool)) else value
                    metadatas.append(metadata)

                # Upsert current batch to ChromaDB
                collection.upsert(
                    ids=ids,
                    embeddings=embeddings,
                    documents=documents,
                    metadatas=metadatas
                )

                print(f"ChromaDB upserted {len(batch_data)} records into collection '{collection_name}' (Batch {batch_index + 1}/{num_batches}).")

        except Exception as e:
            print(f"Error upserting data to collection '{collection_name}': {e}")

    def search(self, collection_name: str, query_texts: List[str], top_k: int = 3,
               simple_output: bool = True, filter: Optional[str] = None,
               output_fields: Optional[List[str]] = None, search_params: Optional[Dict] = None):
        """Search for similar texts in the ChromaDB collection."""
        try:
            collection = self.chroma_client.get_collection(collection_name)

            if not self.sentence_transformer:
                print("Warning: No sentence transformer available for search")
                return []

            # Generate embeddings for query texts
            query_embeddings = self.sentence_transformer.encode(query_texts).tolist()

            # Convert Milvus-style filter to ChromaDB where clause if provided
            where_clause = None
            if filter:
                # Simple conversion for basic filters like 'category == "Concept"'
                # This is a basic implementation - more complex filters would need more sophisticated parsing
                if "==" in filter:
                    parts = filter.split("==")
                    if len(parts) == 2:
                        field = parts[0].strip()
                        value = parts[1].strip().strip('"\'')
                        where_clause = {field: value}
                # Deal with filters like this: filter=f'category in {category_map[keywords_type]}'. ChromaDB does not support 'IN' operator
                # Instead, we will perform multiple searches and combine the results
                

            # Perform search
            results = collection.query(
                query_embeddings=query_embeddings,
                n_results=top_k,
                where=where_clause
            )

            if simple_output:
                # Return unique document names
                unique_names = set()
                if results and 'documents' in results:
                    for doc_list in results['documents']:
                        for doc in doc_list:
                            unique_names.add(doc)
                return list(unique_names)
            else:
                # Return results in a format similar to Milvus
                formatted_results = []
                if results and 'documents' in results:
                    for i, doc_list in enumerate(results['documents']):
                        for j, doc in enumerate(doc_list):
                            result_item = {
                                'entity': {
                                    'name': doc
                                }
                            }
                            # Add metadata if available
                            if 'metadatas' in results and i < len(results['metadatas']) and j < len(results['metadatas'][i]):
                                metadata = results['metadatas'][i][j]
                                if metadata:
                                    result_item['entity'].update(metadata)

                            # Add distance/score if available
                            if 'distances' in results and i < len(results['distances']) and j < len(results['distances'][i]):
                                result_item['distance'] = results['distances'][i][j]

                            formatted_results.append(result_item)

                return [formatted_results]  # Wrap in list to match Milvus format

        except Exception as e:
            print(f"Error searching collection '{collection_name}': {e}")
            return []

    def query(self, collection_name, filter, output_fields):
        """Query operation for ChromaDB."""
        try:
            collection = self.chroma_client.get_collection(collection_name)

            # Convert filter to where clause
            where_clause = None
            if filter:
                if "==" in filter:
                    parts = filter.split("==")
                    if len(parts) == 2:
                        field = parts[0].strip()
                        value = parts[1].strip().strip('"\'')
                        where_clause = {field: value}

            # Get all results matching the filter
            results = collection.get(where=where_clause)

            # Format results to match expected structure
            formatted_results = []
            if results and 'documents' in results:
                for i, doc in enumerate(results['documents']):
                    result_item = {'name': doc}

                    # Add metadata
                    if 'metadatas' in results and i < len(results['metadatas']):
                        metadata = results['metadatas'][i]
                        if metadata:
                            result_item.update(metadata)

                    # Add ID as label_id
                    if 'ids' in results and i < len(results['ids']):
                        try:
                            result_item['label_id'] = int(results['ids'][i])
                        except ValueError:
                            result_item['label_id'] = results['ids'][i]

                    formatted_results.append(result_item)

            return formatted_results

        except Exception as e:
            print(f"Error querying collection '{collection_name}': {e}")
            return []

    def delete_collection(self, collection_name: str):
        """Delete a ChromaDB collection."""
        try:
            self.chroma_client.delete_collection(collection_name)
            print(f"ChromaDB collection '{collection_name}' deleted successfully.")
        except Exception as e:
            print(f"Error deleting collection '{collection_name}': {e}")


class EmbeddingManager:
    def __init__(self, milvus_uri: str = "./milvus.db", embedding_model: str = "all-MiniLM-L6-v2",
                 openai_api_key: Optional[str] = None):
        """
        Initialize the EmbeddingManager.

        :param milvus_uri: Milvus uri.
        :param embedding_model: Name of the Sentence Transformer model or 'openai' for OpenAI embeddings.
        :param openai_api_key: OpenAI API key if using OpenAI embeddings.
        """
        self.embedding_model = embedding_model
        self.openai_api_key = openai_api_key

        # Check if we're on Windows or if Milvus Lite is not available
        if platform.system() == "Windows":
            print("Windows detected. Using LanceDB embedding manager instead of Milvus.")
            self._use_lance = True
            self.lance_manager = LanceEmbeddingManager(embedding_model, openai_api_key)
            return

        try:
            from pymilvus import MilvusClient, DataType, model
            self.milvus_client = MilvusClient(uri=milvus_uri)
            self._use_lance = False

            if self.embedding_model.startswith("text-embedding") and self.openai_api_key:
                self.embedding_ef = model.dense.OpenAIEmbeddingFunction(
                    model_name=self.embedding_model, # Specify the model name
                    api_key=self.openai_api_key, # Provide your OpenAI API key
                    dimensions=1536 # Set the embedding dimensionality
                )
            else:
                # print("OpenAI API key not provided. Using SentenceTransformer embeddings.")
                self.embedding_ef = model.dense.SentenceTransformerEmbeddingFunction(
                    model_name=self.embedding_model, # Specify the model name
                    device='cpu' # Specify the device to use, e.g., 'cpu' or 'cuda:0'
                )
        except Exception as e:
            print(f"Could not initialize Milvus client: {e}")
            print("Falling back to LanceDB embedding manager.")
            self._use_lance = True
            self.lance_manager = LanceEmbeddingManager(embedding_model, openai_api_key)


    def create_collection(self, collection_name: str = "default"):
        """
        Create a Milvus collection with dynamic schema based on the embedding dimension.

        :param collection_name: Name of the collection.
        """
        if self._use_lance:
            return self.lance_manager.create_collection(collection_name)

        if self.milvus_client.has_collection(collection_name):
            print(f"Collection '{collection_name}' already exists.")
            return "exists"

        # Calculate embedding dimension dynamically
        embedding_dimension = len(self.embedding_ef(['sample_text'])[0])

        # Define the schema dynamically
        from pymilvus import DataType
        schema = self.milvus_client.create_schema(
            enable_dynamic_field=True,
        )
        schema.add_field(field_name="label_id", datatype=DataType.INT64, is_primary=True)
        schema.add_field(field_name="name", datatype=DataType.VARCHAR, max_length=512)
        schema.add_field(field_name="embedding", datatype=DataType.FLOAT_VECTOR, dim=embedding_dimension)
        schema.add_field(field_name="category", datatype=DataType.VARCHAR, default_value="general", max_length=512)

        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="embedding",
            index_type="AUTOINDEX",
            metric_type="COSINE"
        )

        self.milvus_client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)
        print(f"Collection '{collection_name}' created successfully with embedding dimension {embedding_dimension}.")
        return "success"

    def upsert_data(self, collection_name: str, data: List[Dict]):
        """
        Insert data into the Milvus collection dynamically in batches.

        :param collection_name: Name of the collection.
        :param data: List of dictionaries containing the data.
        """
        if self._use_lance:
            return self.lance_manager.upsert_data(collection_name, data)

        batch_size = 10000

        # Split data into batches
        num_batches = ceil(len(data) / batch_size)

        for batch_index in range(num_batches):
            # Get the current batch of data
            start_idx = batch_index * batch_size
            end_idx = min((batch_index + 1) * batch_size, len(data))
            batch_data = data[start_idx:end_idx]

            # Extract texts and generate embeddings for the current batch
            texts = [item["name"] for item in batch_data]
            embeddings = self.embedding_ef(texts)

            # Add embeddings to the batch data
            for index, item in enumerate(batch_data):
                item["embedding"] = embeddings[index]

            # Perform upsert for the current batch
            self.milvus_client.upsert(collection_name=collection_name, data=batch_data)
            print(f"Upserted {len(batch_data)} records into collection '{collection_name}' (Batch {batch_index + 1}/{num_batches}).")

        print(self.milvus_client.describe_collection(collection_name=collection_name))


    def load_collection(self, collection_name: str, load_fields=["label_id", "name", "embedding", "category"], skip_load_dynamic_field=True):
        """
        Load the specified Milvus collection.

        :param collection_name: Name of the collection.
        """
        if self._use_lance:
            return "Using LanceDB, no need to load collection."

        self.milvus_client.load_collection(collection_name=collection_name, load_fields=load_fields, skip_load_dynamic_field=skip_load_dynamic_field)

    def search(self, collection_name: str, query_texts: List[str], top_k: int = 3, simple_output: bool = True, filter: Optional[str] = None, output_fields: Optional[List[str]] = ["name"], search_params: Optional[Dict] = {"metric_type": "COSINE"}):
        """
        Search for similar texts in the Milvus collection.

        :param collection_name: Name of the collection.
        :param query_texts: a list of texts to search for.
        :param top_k: Number of top results to return.
        :param simple_output: If True, return a simplified list of unique labels. The output_fields argument may be suppressed but must at least has "name". If not, the original search results from milvus_client are returned.
        :param filter: Optional filter expression.
        :param output_fields: Optional list of fields to return. The primary field will be returned if not set.
        :return: List of search results.
        """
        if self._use_lance:
            return self.lance_manager.search(collection_name, query_texts, top_k, simple_output, filter, output_fields, search_params)

        query_embeddings = self.embedding_ef(query_texts)
        # output_fields = ["Record_Type"] if output_fields is None else output_fields

        results = self.milvus_client.search(collection_name=collection_name, data=query_embeddings, limit=top_k, filter=filter, output_fields=output_fields, search_params=search_params)

        if simple_output:
            label_set = set()
            for sublist in results:
                for item in sublist:
                    if 'entity' in item and 'name' in item['entity']:
                        label_set.add(item['entity']['name'])
            return list(label_set)
        else:
            return results

    def query(self, collection_name, filter, output_fields):
        if self._use_lance:
            return self.lance_manager.query(collection_name, filter, output_fields)

        return self.milvus_client.query(collection_name=collection_name, filter=filter, output_fields=output_fields)

    def delete_collection(self, collection_name: str):
        """
        Delete a Milvus or LanceDB collection.

        :param collection_name: Name of the collection.
        """
        if self._use_lance:
            return self.lance_manager.delete_collection(collection_name)

        if self.milvus_client.has_collection(collection_name):
            self.milvus_client.drop_collection(collection_name)
            print(f"Collection '{collection_name}' deleted successfully.")
        else:
            print(f"Collection '{collection_name}' does not exist.")


# Example usage
if __name__ == "__main__":
    # Initialize the EmbeddingManager
    embedding_manager = EmbeddingManager(embedding_model="all-MiniLM-L6-v2")

    current_directory = os.path.dirname(os.path.abspath(__file__))
    label_pool_path = os.path.join(current_directory, 'label_pool.csv')
    has_header_input = input("Does the CSV file have a header? (yes/no): ").strip().lower()
    has_header = has_header_input == 'yes'
    json_data = csv_to_json(label_pool_path, delimiter='#', has_header=has_header)
    # print("JSON Data:\n", json_data)

    # Create a collection and upsert data
    res = embedding_manager.create_collection("test_collection")
    if res == "success":
        embedding_manager.upsert_data("test_collection", json_data)

    # Search
    results = embedding_manager.search("test_collection", ["industrial revolution", "Japanese empire"], top_k=3, simple_output=True, filter='category == "Concept"', search_params={"metric_type": "COSINE", "params":{"radius": 0.2}})
    
    print("Search Results:", results)

    # # Delete the collection
    # embedding_manager.delete_collection("test_collection")