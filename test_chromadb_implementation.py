#!/usr/bin/env python3
"""
Test script for LanceDB implementation on all platforms.
This script tests the LanceEmbeddingManager to ensure it provides the same functionality as the Milvus version.
"""

import os
import sys
import platform
import tempfile
import shutil
from typing import List, Dict

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.embeddings_management import Embedding<PERSON>anager, LanceEmbeddingManager
from app.core.csv_converter import csv_to_json

def test_lancedb_basic_functionality():
    """Test basic LanceDB functionality."""
    print("=" * 60)
    print("Testing LanceDB Basic Functionality")
    print("=" * 60)

    # Create a temporary directory for LanceDB
    temp_dir = tempfile.mkdtemp(prefix="lancedb_test_")
    print(f"Using temporary directory: {temp_dir}")

    try:
        # Initialize LanceDB manager
        lance_manager = LanceEmbeddingManager(
            embedding_model="all-MiniLM-L6-v2",
            persist_directory=temp_dir
        )
        
        # Test data
        test_data = [
            {"label_id": 1, "name": "Machine Learning", "category": "Concept"},
            {"label_id": 2, "name": "Artificial Intelligence", "category": "Concept"},
            {"label_id": 3, "name": "Deep Learning", "category": "Concept"},
            {"label_id": 4, "name": "Neural Networks", "category": "Concept"},
            {"label_id": 5, "name": "Natural Language Processing", "category": "Concept"},
        ]
        
        collection_name = "test_collection"
        
        # Test 1: Create collection
        print("\n1. Testing collection creation...")
        result = lance_manager.create_collection(collection_name)
        assert result == "success", f"Expected 'success', got '{result}'"
        print("✓ Collection created successfully")

        # Test 2: Create collection again (should return 'exists')
        print("\n2. Testing duplicate collection creation...")
        result = lance_manager.create_collection(collection_name)
        assert result == "exists", f"Expected 'exists', got '{result}'"
        print("✓ Duplicate collection handling works correctly")

        # Test 3: Upsert data
        print("\n3. Testing data upsert...")
        lance_manager.upsert_data(collection_name, test_data)
        print("✓ Data upserted successfully")

        # Test 4: Load collection (no-op for LanceDB)
        print("\n4. Testing collection loading...")
        lance_manager.load_collection(collection_name)
        print("✓ Collection loaded successfully")
        
        # Test 5: Search functionality
        print("\n5. Testing search functionality...")
        query_texts = ["machine learning algorithms", "AI technology"]
        results = lance_manager.search(collection_name, query_texts, top_k=3, simple_output=True)
        print(f"Search results (simple): {results}")
        assert isinstance(results, list), "Results should be a list"
        assert len(results) > 0, "Should return some results"
        print("✓ Simple search works correctly")

        # Test 6: Search with detailed output
        print("\n6. Testing detailed search...")
        detailed_results = lance_manager.search(collection_name, query_texts, top_k=3, simple_output=False)
        print(f"Detailed search results: {detailed_results}")
        assert isinstance(detailed_results, list), "Detailed results should be a list"
        print("✓ Detailed search works correctly")

        # Test 7: Search with filter
        print("\n7. Testing search with filter...")
        filtered_results = lance_manager.search(
            collection_name,
            query_texts,
            top_k=3,
            simple_output=True,
            filter='category == "Concept"'
        )
        print(f"Filtered search results: {filtered_results}")
        assert isinstance(filtered_results, list), "Filtered results should be a list"
        print("✓ Filtered search works correctly")

        # Test 8: Query functionality
        print("\n8. Testing query functionality...")
        query_results = lance_manager.query(collection_name, 'category == "Concept"', ["name", "category"])
        print(f"Query results: {query_results}")
        assert isinstance(query_results, list), "Query results should be a list"
        assert len(query_results) > 0, "Should return some query results"
        print("✓ Query functionality works correctly")

        # Test 9: Delete collection
        print("\n9. Testing collection deletion...")
        lance_manager.delete_collection(collection_name)
        print("✓ Collection deleted successfully")
        
        print("\n" + "=" * 60)
        print("✅ All ChromaDB basic functionality tests passed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up temporary directory (with Windows-specific handling)
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
                print(f"Cleaned up temporary directory: {temp_dir}")
            except PermissionError as e:
                print(f"Warning: Could not clean up temporary directory due to Windows file locks: {e}")
                print(f"Temporary directory left at: {temp_dir}")
    
    return True

def test_embedding_manager_integration():
    """Test EmbeddingManager integration with ChromaDB on Windows."""
    print("\n" + "=" * 60)
    print("Testing EmbeddingManager Integration")
    print("=" * 60)
    
    try:
        # Initialize EmbeddingManager (should use ChromaDB on Windows)
        embedding_manager = EmbeddingManager(embedding_model="all-MiniLM-L6-v2")
        
        # Check if it's using ChromaDB on Windows
        if platform.system() == "Windows":
            assert hasattr(embedding_manager, '_use_chroma'), "Should have _use_chroma attribute on Windows"
            assert embedding_manager._use_chroma, "Should be using ChromaDB on Windows"
            print("✓ EmbeddingManager correctly uses ChromaDB on Windows")
        else:
            print("ℹ️  Not on Windows, skipping Windows-specific tests")
            return True
        
        # Test data
        test_data = [
            {"label_id": 1, "name": "Computer Science", "category": "Field"},
            {"label_id": 2, "name": "Software Engineering", "category": "Field"},
            {"label_id": 3, "name": "Data Science", "category": "Field"},
        ]
        
        collection_name = "integration_test_collection"
        
        # Test collection creation through EmbeddingManager
        print("\n1. Testing collection creation through EmbeddingManager...")
        result = embedding_manager.create_collection(collection_name)
        assert result in ["success", "exists"], f"Expected 'success' or 'exists', got '{result}'"
        print("✓ Collection creation works through EmbeddingManager")
        
        # Test data upsert through EmbeddingManager
        print("\n2. Testing data upsert through EmbeddingManager...")
        embedding_manager.upsert_data(collection_name, test_data)
        print("✓ Data upsert works through EmbeddingManager")
        
        # Test search through EmbeddingManager
        print("\n3. Testing search through EmbeddingManager...")
        search_results = embedding_manager.search(collection_name, ["programming", "software"], top_k=2)
        print(f"Search results: {search_results}")
        assert isinstance(search_results, list), "Search results should be a list"
        print("✓ Search works through EmbeddingManager")
        
        # Test query through EmbeddingManager
        print("\n4. Testing query through EmbeddingManager...")
        query_results = embedding_manager.query(collection_name, 'category == "Field"', ["name"])
        print(f"Query results: {query_results}")
        assert isinstance(query_results, list), "Query results should be a list"
        print("✓ Query works through EmbeddingManager")
        
        # Clean up
        print("\n5. Testing collection deletion through EmbeddingManager...")
        embedding_manager.delete_collection(collection_name)
        print("✓ Collection deletion works through EmbeddingManager")
        
        print("\n" + "=" * 60)
        print("✅ All EmbeddingManager integration tests passed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Integration test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """Run all tests."""
    print("ChromaDB Implementation Test Suite")
    print(f"Platform: {platform.system()}")
    print(f"Python version: {sys.version}")
    
    all_tests_passed = True
    
    # Test 1: Basic ChromaDB functionality
    if not test_chromadb_basic_functionality():
        all_tests_passed = False
    
    # Test 2: EmbeddingManager integration
    if not test_embedding_manager_integration():
        all_tests_passed = False
    
    # Final result
    print("\n" + "=" * 80)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED! ChromaDB implementation is working correctly.")
    else:
        print("❌ SOME TESTS FAILED! Please check the errors above.")
    print("=" * 80)
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
